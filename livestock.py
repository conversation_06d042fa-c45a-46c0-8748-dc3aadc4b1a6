import pulp

# Create a linear programming problem
prob = pulp.LpProblem("Maximize_Net_Revenue", pulp.LpMaximize)

minHead = 80

# Decision variables for the number of each animal
cows = pulp.LpVariable("Cows", lowBound=minHead, cat='Integer')  # Minimum 10 cows
goats = pulp.LpVariable("Goats", lowBound=minHead, cat='Integer') # Minimum 20 goats
sheep = pulp.LpVariable("Sheep", lowBound=minHead, cat='Integer') # Minimum 15 sheep
swine = pulp.LpVariable("Swine", lowBound=minHead, cat='Integer') # Minimum 30 swine

# Revenue per animal type
revenue_cows = 214
revenue_goats = 46
revenue_sheep = 38
revenue_swine = 17

# Winter feed cost per animal type
feed_cost_cows = 25
feed_cost_goats = 4
feed_cost_sheep = 3
feed_cost_swine = 0.25

# Acres of land required per animal type
acres_cows = 2
acres_goats = 0.5
acres_sheep = 1/3
acres_swine = 0.1

# Man-days of labor required per animal type
labor_cows = 20
labor_goats = 5
labor_sheep = 3
labor_swine = 2

# Calculate net revenue
net_revenue = (revenue_cows - feed_cost_cows) * cows + \
              (revenue_goats - feed_cost_goats) * goats + \
              (revenue_sheep - feed_cost_sheep) * sheep + \
              (revenue_swine - feed_cost_swine) * swine

# Add the objective function to the problem
prob += net_revenue, "Net_Revenue"

# Constraints
# Land constraint (total acres of land available)
land_constraint = (acres_cows * cows + acres_goats * goats + acres_sheep * sheep + acres_swine * swine)
prob += (land_constraint <= 1742), "Land_Constraint"

# Labor constraint (total man-days of labor available)
labor_constraint = (labor_cows * cows + labor_goats * goats + labor_sheep * sheep + labor_swine * swine)
prob += (labor_constraint <= 17451), "Labor_Constraint"

# Solve the problem
prob.solve()

# Print the results
if pulp.LpStatus[prob.status] == 'Optimal':
    # Calculate the number of acres used for each type of animal
    acres_used_cows = acres_cows * cows.varValue
    acres_used_goats = acres_goats * goats.varValue
    acres_used_sheep = acres_sheep * sheep.varValue
    acres_used_swine = acres_swine * swine.varValue

    # Total acres and man-days utilized
    total_acres_used = acres_used_cows + acres_used_goats + acres_used_sheep + acres_used_swine
    total_labor_used = (labor_cows * cows.varValue + labor_goats * goats.varValue +
                        labor_sheep * sheep.varValue + labor_swine * swine.varValue)

    print(f"Acres of land devoted to cows: {acres_used_cows:.0f}")
    print(f"Acres of land devoted to goats: {acres_used_goats:.0f}")
    print(f"Acres of land devoted to sheep: {acres_used_sheep:.0f}")
    print(f"Acres of land devoted to swine: {acres_used_swine:.0f}")
    print(f"Total Acres Used: {total_acres_used:.0f}")
    print(f"Total Man-Days Used: {total_labor_used:.0f}")
    print(f"Maximum Net Revenue: ${pulp.value(prob.objective):.2f}")
else:
    print("No optimal solution found.")


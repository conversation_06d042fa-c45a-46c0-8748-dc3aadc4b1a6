import pulp

# Create a linear programming problem
prob = pulp.LpProblem("Maximize_Crop_Revenue", pulp.LpMaximize)

minAcres=200

# Decision variables for the number of acres allocated to each crop
rye = pulp.LpVariable("Rye", lowBound=minAcres, cat='Integer')
barley = pulp.LpVariable("Barley", lowBound=minAcres, cat='Integer')
oats = pulp.LpVariable("Oats", lowBound=minAcres, cat='Integer')
vegetables = pulp.LpVariable("Vegetables", lowBound=minAcres, cat='Integer')
flax = pulp.LpVariable("Flax", lowBound=minAcres, cat='Integer')
hay = pulp.LpVariable("Hay", lowBound=minAcres, cat='Integer')
wheat = pulp.LpVariable("Wheat", lowBound=minAcres, cat='Integer')
fruit = pulp.LpVariable("Fruit", lowBound=minAcres, cat='Integer')

# Revenue per acre for each crop
revenue_rye = 52
revenue_barley = 56
revenue_oats = 49
revenue_vegetables = 92
revenue_flax = 75
revenue_hay = 47
revenue_wheat = 82
revenue_fruit = 102

# Man-days of labor required per acre for each crop
labor_rye = 5+1.7
labor_barley = 6+1.7
labor_oats = 5+1.7
labor_vegetables = 8+1.7
labor_flax = 6+1.7
labor_hay = 5+1.7
labor_wheat = 6+1.7
labor_fruit = 7+1.7

# Add the objective function to the problem (maximize total revenue)
prob += (revenue_rye * rye + revenue_barley * barley + revenue_oats * oats +
         revenue_vegetables * vegetables + revenue_flax * flax + 
         revenue_hay * hay + revenue_wheat * wheat + revenue_fruit * fruit), "Total_Revenue"

# Constraints
# Land constraint (total acres of land available)
land_constraint = (rye + barley + oats + vegetables + flax + hay + wheat + fruit)
prob += (land_constraint <= 1811), "Land_Constraint"

# Labor constraint (total man-days of labor available)
labor_constraint = (labor_rye * rye + labor_barley * barley + labor_oats * oats +
                    labor_vegetables * vegetables + labor_flax * flax + 
                    labor_hay * hay + labor_wheat * wheat + labor_fruit * fruit)
prob += (labor_constraint <= 13503), "Labor_Constraint"

# Solve the problem
prob.solve()

# Print the results
if pulp.LpStatus[prob.status] == 'Optimal':
    print(f"Acres allocated to Rye: {rye.varValue:.0f}")
    print(f"Acres allocated to Barley: {barley.varValue:.0f}")
    print(f"Acres allocated to Oats: {oats.varValue:.0f}")
    print(f"Acres allocated to Vegetables: {vegetables.varValue:.0f}")
    print(f"Acres allocated to Flax: {flax.varValue:.0f}")
    print(f"Acres allocated to Hay: {hay.varValue:.0f}")
    print(f"Acres allocated to Wheat: {wheat.varValue:.0f}")
    print(f"Acres allocated to Fruit: {fruit.varValue:.0f}")
    
    total_acres_used = rye.varValue + barley.varValue + oats.varValue + vegetables.varValue + flax.varValue + hay.varValue + wheat.varValue + fruit.varValue
    total_labor_used = (labor_rye * rye.varValue + labor_barley * barley.varValue + labor_oats * oats.varValue +
                        labor_vegetables * vegetables.varValue + labor_flax * flax.varValue + 
                        labor_hay * hay.varValue + labor_wheat * wheat.varValue + labor_fruit * fruit.varValue)
    
    print(f"Total Acres Used: {total_acres_used:.0f}")
    print(f"Total Man-Days Used: {total_labor_used:.0f}")
    print(f"Maximum Revenue: ${pulp.value(prob.objective):.2f}")
else:
    print("No optimal solution found.")


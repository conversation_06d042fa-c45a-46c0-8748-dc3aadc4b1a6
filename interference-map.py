import math
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import matplotlib.pyplot as plt
import matplotlib.patches as patches

# --- CONFIG ---
LAMBDA = 100.00 # wavelength in pixels (same for all sources here)
W, H = int(LAMBDA * 20) + 1, int(LAMBDA * 20) + 1
K = 2.0 * np.pi / LAMBDA
FARFIELD = 20 * LAMBDA   # Far-field sampling radius in pixels (independent of image size)
                         # This ensures consistent antenna calculations regardless of output image dimensions
                         # Typical values: 8-15 wavelengths for accurate far-field approximation
                         # Enhanced color mapping with multiple stops for better depth and contrast
COLOR_STOPS = [
  (0.00, (0, 0, 0)),         # Pure black for truly zero signals
  (0.05, (0, 15, 40)),      # Very dark blue for very low signals (-20dB+)
  (0.15, (0, 35, 85)),      # Dark blue for low signals (-10dB range)
  (0.35, (0, 85, 160)),     # Medium blue for mid-low signals
  (0.65, (0, 135, 215)),    # Bright blue for mid-high signals
  (0.85, (0, 175, 245)),    # Brighter blue for high signals
  (1.00, (20, 220, 255))     # Bright cyan with slight green tint for maximum signals
]

# Text rendering configuration
TEXT_FONT_SIZE = 2 * min(W, H) / LAMBDA
TEXT_LINE_HEIGHT = TEXT_FONT_SIZE * 1.4  # Line height scales with font size (1.4x is typical)
TEXT_LETTER_SPACING = 2  # Extra pixels between characters (0 = default, 2-3 = more spaced)
TEXT_FONT_WEIGHT = "light"  # "light", "regular", "bold"
TEXT_MARGIN = int(TEXT_FONT_SIZE * 0.8)  # Margin from edges, scales with font size


# Radiators: name -> dict(x, y, amp, phase)
# - amp: relative amplitude (1.0 = equal power)
# - phase: initial phase offset in degrees (0 by default)
#
# PHYSICAL MEANING OF PHASE RELATIONSHIPS:
# Phase differences between radiators control the interference pattern and beam steering:
# - 0° phase difference: Elements radiate in-phase, creating constructive interference
#   in the direction perpendicular to the array axis (broadside radiation)
# - 90° phase difference: Creates a quadrature relationship, useful for:
#   * Circular polarization when elements are orthogonally positioned
#   * Beam steering and pattern shaping in phased arrays
#   * Reducing mutual coupling between closely spaced elements
# - 180° phase difference: Elements radiate out-of-phase, creating nulls in certain
#   directions and can be used for beam steering or interference cancellation
#
# In this configuration:
# - Element A (center): 0° reference phase
# - Elements B & C (sides): 90° phase lead, creating asymmetric pattern with
#   controlled interference between the three elements
RADIATOR_SPACING_CONSTANT = LAMBDA  # or whatever distance you want between elements

# Linear array (bidirectional) - uncomment to test symmetric patterns
RADIATORS = {
  "A": {"offset-x": 0, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
  "B": {"offset-x": RADIATOR_SPACING_CONSTANT, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
  "C": {"offset-x": -RADIATOR_SPACING_CONSTANT, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
  "D": {"offset-x": 2*RADIATOR_SPACING_CONSTANT, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
  "E": {"offset-x": -2*RADIATOR_SPACING_CONSTANT, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
  "F": {"offset-x": 3*RADIATOR_SPACING_CONSTANT, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
  "G": {"offset-x": -3*RADIATOR_SPACING_CONSTANT, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
  "H": {"offset-x": 4*RADIATOR_SPACING_CONSTANT, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
  "I": {"offset-x": -4*RADIATOR_SPACING_CONSTANT, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
  "J": {"offset-x": 5*RADIATOR_SPACING_CONSTANT, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
  "K": {"offset-x": -5*RADIATOR_SPACING_CONSTANT, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
  "L": {"offset-x": 6*RADIATOR_SPACING_CONSTANT, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
  "M": {"offset-x": -6*RADIATOR_SPACING_CONSTANT, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
  "N": {"offset-x": 7*RADIATOR_SPACING_CONSTANT, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
  "O": {"offset-x": -7*RADIATOR_SPACING_CONSTANT, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
}

# Triangle Array
# RADIATORS = {
#   "A": {
#     "offset-x": -(math.sqrt(3)/3) * RADIATOR_SPACING_CONSTANT,
#     "offset-y": 0.0,
#     "x": 0, "y": 0, "amp": 1.0, "phase": 0.0
#   },
#   "B": {
#     "offset-x": (math.sqrt(3)/6) * RADIATOR_SPACING_CONSTANT,
#     "offset-y": 0.5 * RADIATOR_SPACING_CONSTANT,
#     "x": 0, "y": 0, "amp": 1.0, "phase": 90.0
#   },
#   "C": {
#     "offset-x": (math.sqrt(3)/6) * RADIATOR_SPACING_CONSTANT,
#     "offset-y": -0.5 * RADIATOR_SPACING_CONSTANT,
#     "x": 0, "y": 0, "amp": 1.0, "phase": 90.0
#   },
# }

# Square Array
# RADIATORS = {
#   "A": {
#     "offset-x": 0.5 * RADIATOR_SPACING_CONSTANT,
#     "offset-y": 0.5 * RADIATOR_SPACING_CONSTANT,
#     "x": 0, "y": 0, "amp": 1.0, "phase": 0.0
#   },
#   "B": {
#     "offset-x": -0.5 * RADIATOR_SPACING_CONSTANT,
#     "offset-y": 0.5 * RADIATOR_SPACING_CONSTANT,
#     "x": 0, "y": 0, "amp": 1.0, "phase": 0.0
#   },
#   "C": {
#     "offset-x": 0.5 * RADIATOR_SPACING_CONSTANT,
#     "offset-y": -0.5 * RADIATOR_SPACING_CONSTANT,
#     "x": 0, "y": 0, "amp": 1.0, "phase": 0.0
#   },
#   "D": {
#     "offset-x": -0.5 * RADIATOR_SPACING_CONSTANT,
#     "offset-y": -0.5 * RADIATOR_SPACING_CONSTANT,
#     "x": 0, "y": 0, "amp": 1.0, "phase": 0.0
#   },
# }

# Single isotropic radiator (omnidirectional) - uncomment to test
# RADIATORS = {
#   "A": {"offset-x": 0, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
# }

# Linear Array
# RADIATORS = {
#   "A": {"offset-x": 0, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "B": {"offset-x": LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "C": {"offset-x": -LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "D": {"offset-x": 2*LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "E": {"offset-x": -2*LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "F": {"offset-x": 3*LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "G": {"offset-x": -3*LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "H": {"offset-x": 4*LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "I": {"offset-x": -4*LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "J": {"offset-x": 5*LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
#   "K": {"offset-x": -5*LAMBDA, "offset-y": 0, "x": 0, "y": 0, "amp": 1.0, "phase": 0.0},
# }

# --- HELPER FUNCTIONS ---
def _bilinear_sample(arr, xs, ys):
  """
  Bilinear sample of 2D array `arr` at floating coords (xs, ys).
  xs, ys are 1D arrays of same length.
  Returns 1D array of sampled values.
  """
  h, w = arr.shape
  x0 = np.floor(xs).astype(np.int32)
  y0 = np.floor(ys).astype(np.int32)
  x1 = np.clip(x0 + 1, 0, w - 1)
  y1 = np.clip(y0 + 1, 0, h - 1)

  x0 = np.clip(x0, 0, w - 1)
  y0 = np.clip(y0, 0, h - 1)

  dx = xs - x0
  dy = ys - y0

  Ia = arr[y0, x0]
  Ib = arr[y0, x1]
  Ic = arr[y1, x0]
  Id = arr[y1, x1]

  top = Ia * (1 - dx) + Ib * dx
  bot = Ic * (1 - dx) + Id * dx
  return top * (1 - dy) + bot * dy


def _unwrap_angle(a):
  """Map angle to (-pi, pi]."""
  return (a + np.pi) % (2 * np.pi) - np.pi


def _interp_crossing(a0, v0, a1, v1, v_target):
  """
  Linear interpolation of the angle where v crosses v_target between (a0,v0) and (a1,v1).
  Angles are assumed already unwrapped (monotonic segment).
  """
  t = (v_target - v0) / (v1 - v0 + 1e-12)
  return a0 + t * (a1 - a0)


def _compute_extended_field(cx, cy, ring_radius, radiators, lamb_px):
  """
  Compute intensity field in an extended region around (cx, cy) to accommodate
  far-field sampling at the specified ring radius.

  This function is called when the requested far-field sampling radius (FARFIELD)
  extends beyond the boundaries of the main intensity calculation. It ensures
  that antenna parameters (HPBW, F/B ratio, directivity) are calculated at a
  consistent electrical distance from the radiators, regardless of the output
  image size.

  Args:
    cx, cy: center coordinates in the original coordinate system
    ring_radius: required sampling radius in pixels
    radiators: dictionary of radiator configurations
    lamb_px: wavelength in pixels

  Returns:
    I_extended: 2D intensity array covering the extended region
    offset_x, offset_y: offsets to map original coordinates to extended field
  """
  # Calculate required field size to accommodate the sampling ring
  margin = int(ring_radius * 1.1)  # Add 10% margin for safety
  field_size = 2 * margin + 1

  # Calculate offsets to center the original coordinates in the extended field
  offset_x = margin - cx
  offset_y = margin - cy

  # Create coordinate grids for the extended field
  y_ext, x_ext = np.mgrid[0:field_size, 0:field_size]

  # Adjust coordinates to match the original coordinate system
  x_ext = x_ext - offset_x
  y_ext = y_ext - offset_y

  # Compute field using the same method as the main calculation
  K = 2.0 * np.pi / lamb_px
  E_real = np.zeros((field_size, field_size), dtype=np.float64)
  E_imag = np.zeros((field_size, field_size), dtype=np.float64)

  for r in radiators.values():
    dx = x_ext - r["x"]
    dy = y_ext - r["y"]
    dist = np.hypot(dx, dy)
    phase_total = K * dist + np.deg2rad(r.get("phase", 0.0))
    amp = r.get("amp", 1.0)
    E_real += amp * np.cos(phase_total)
    E_imag += amp * np.sin(phase_total)

  # Calculate intensity
  I_extended = E_real**2 + E_imag**2

  # Normalize by theoretical max (sum of amplitudes)^2
  amp_sum = sum(r.get("amp", 1.0) for r in radiators.values())
  I_extended /= (amp_sum ** 2 + 1e-12)
  I_extended = np.clip(I_extended, 0.0, 1.0)

  return I_extended, offset_x, offset_y



def estimate_hpbw(I, cx, cy, lamb_px, ring_choice_px=None, num_samples=2048, threshold_db=3.0, omni_threshold=0.1):
  """
  Estimate HPBW by sampling intensity on a far-field ring around (cx, cy).

  Args:
    I: 2D intensity array
    cx, cy: center coordinates
    lamb_px: wavelength in pixels
    ring_choice_px: optional ring radius in pixels (uses FARFIELD if None)
    num_samples: number of angular samples around the ring
    threshold_db: threshold in dB below peak for beamwidth calculation
    omni_threshold: maximum variation (as fraction of peak) to consider omnidirectional

  Returns (theta_left, theta_right, hpbw_rad, theta_peak, is_omnidirectional).
  For omnidirectional patterns, returns (None, None, 2*pi, None, True).
  """
  # Use fixed far-field radius independent of image size for consistent results
  if ring_choice_px is None:
    ring = int(FARFIELD)
  else:
    ring = int(ring_choice_px)

  angles = np.linspace(-np.pi, np.pi, num_samples, endpoint=False)
  xs = cx + ring * np.cos(angles)
  ys = cy + ring * np.sin(angles)
  I_theta = _bilinear_sample(I, xs, ys)

  # Peak and threshold level
  idx_peak = int(np.argmax(I_theta))
  I_peak = I_theta[idx_peak]
  I_min = np.min(I_theta)

  # Check for omnidirectional pattern (uniform intensity around the ring)
  intensity_variation = (I_peak - I_min) / (I_peak + 1e-12)
  if intensity_variation < omni_threshold:
    # Omnidirectional pattern detected
    return None, None, 2.0 * np.pi, None, True

  # Convert dB threshold to linear scale: threshold_linear = 10^(-threshold_db/10)
  threshold_linear = 10.0 ** (-threshold_db / 10.0)
  threshold_level = threshold_linear * I_peak

  # Search to the "left" of the peak (decreasing index, wrap) for first crossing
  def find_cross(start, step):
    i0 = start
    for _ in range(num_samples):
      i1 = (i0 + step) % num_samples
      v0, v1 = I_theta[i0], I_theta[i1]
      if (v0 - threshold_level) * (v1 - threshold_level) <= 0.0:  # crossed or touched
        # unwrap local angles for interpolation
        a0 = angles[i0]
        a1 = angles[i1]
        # make a1 close to a0 (handle wrap)
        if step == -1 and a1 > a0:
          a1 -= 2 * np.pi
        if step == +1 and a1 < a0:
          a1 += 2 * np.pi
        return _interp_crossing(a0, v0, a1, v1, threshold_level)
      i0 = i1
    return None

  theta_left  = find_cross(idx_peak, -1)
  theta_right = find_cross(idx_peak, +1)

  # Normalize back to (-pi, pi]
  theta_peak = angles[idx_peak]
  theta_left  = _unwrap_angle(theta_left)
  theta_right = _unwrap_angle(theta_right)

  # Compute smallest positive span from left to right going through the peak
  span = _unwrap_angle(theta_right - theta_left)
  if span <= 0:
    span += 2 * np.pi

  return theta_left, theta_right, span, theta_peak, False


def calculate_fb_ratio(I, cx, cy, theta_peak, lamb_px, ring_choice_px=None, num_samples=2048):
  """
  Calculate Front-to-Back (F/B) ratio following IEEE antenna measurement standards.

  Args:
    I: 2D intensity array
    cx, cy: center coordinates
    theta_peak: peak direction angle in radians
    lamb_px: wavelength in pixels
    ring_choice_px: optional ring radius in pixels (uses FARFIELD if None)
    num_samples: number of angular samples around the ring

  Returns:
    fb_ratio_db: Front-to-Back ratio in dB, or None if calculation fails
  """
  if theta_peak is None:
    return None

  # Use fixed far-field radius independent of image size for consistent results
  if ring_choice_px is None:
    ring = int(FARFIELD)
  else:
    ring = int(ring_choice_px)

  # Front direction (peak direction)
  front_x = cx + ring * np.cos(theta_peak)
  front_y = cy + ring * np.sin(theta_peak)

  # Back direction (180° opposite to peak)
  theta_back = _unwrap_angle(theta_peak + np.pi)
  back_x = cx + ring * np.cos(theta_back)
  back_y = cy + ring * np.sin(theta_back)

  # Sample intensities at front and back directions
  front_intensity = _bilinear_sample(I, np.array([front_x]), np.array([front_y]))[0]
  back_intensity = _bilinear_sample(I, np.array([back_x]), np.array([back_y]))[0]

  # Calculate F/B ratio in dB
  # Add small epsilon to avoid log(0) and handle very low back radiation
  epsilon = 1e-12
  fb_ratio_linear = front_intensity / (back_intensity + epsilon)
  fb_ratio_db = 10.0 * np.log10(fb_ratio_linear + epsilon)

  return fb_ratio_db


def find_main_lobes(I, cx, cy, lamb_px, ring_choice_px=None, num_samples=2048, main_lobe_threshold_db=1.0):
  """
  Find all main lobes based on equivalent peak intensity and HPBW.

  A "main lobe" is defined as having both:
  1. Peak radiated power equivalent to the global peak (within intensity_tolerance)
  2. HPBW equivalent to the primary lobe (within hpbw_tolerance)

  This approach works for any number of main lobes (1, 2, 3, 4, 6, 8, etc.)
  without requiring predefined geometric patterns.

  Args:
    I: 2D intensity array
    cx, cy: center coordinates
    lamb_px: wavelength in pixels
    ring_choice_px: optional ring radius in pixels (uses FARFIELD if None)
    num_samples: number of angular samples around the ring
    main_lobe_threshold_db: minimum threshold in dB for lobe detection

  Returns:
    main_lobe_angles: list of angles (in radians) of main lobe peaks
  """
  # Use fixed far-field radius independent of image size for consistent results
  if ring_choice_px is None:
    ring = int(FARFIELD)
  else:
    ring = int(ring_choice_px)

  # Sample intensity around the full ring
  angles = np.linspace(-np.pi, np.pi, num_samples, endpoint=False)
  xs = cx + ring * np.cos(angles)
  ys = cy + ring * np.sin(angles)
  I_theta = _bilinear_sample(I, xs, ys)

  # Find the global peak
  global_peak_idx = np.argmax(I_theta)
  global_peak_intensity = I_theta[global_peak_idx]

  # Define threshold for candidate lobes
  threshold_linear = 10.0 ** (-main_lobe_threshold_db / 10.0)
  candidate_threshold = threshold_linear * global_peak_intensity

  # Find all local maxima that exceed the threshold
  candidate_lobes = []
  neighborhood_size = 20  # Check ±20 samples around each point

  for i in range(num_samples):
    current_intensity = I_theta[i]

    # Only consider points above the candidate threshold
    if current_intensity < candidate_threshold:
      continue

    # Check if this is a local maximum in a wider neighborhood
    is_local_max = True
    for offset in range(-neighborhood_size, neighborhood_size + 1):
      if offset == 0:
        continue
      neighbor_idx = (i + offset) % num_samples
      if I_theta[neighbor_idx] > current_intensity:
        is_local_max = False
        break

    if is_local_max:
      angle = angles[i]

      # Avoid duplicates too close together (within 20 degrees)
      is_duplicate = False
      for existing_angle, _, _ in candidate_lobes:
        angle_diff = abs(_unwrap_angle(angle - existing_angle))
        if angle_diff < np.deg2rad(20):
          is_duplicate = True
          break

      if not is_duplicate:
        # Calculate HPBW for this lobe
        lobe_left, lobe_right = calculate_lobe_hpbw(I, cx, cy, angle, lamb_px, ring_choice_px, num_samples, threshold_db=3.0)

        if lobe_left is not None and lobe_right is not None:
          # Calculate HPBW in radians
          lobe_hpbw = _unwrap_angle(lobe_right - lobe_left)
          if lobe_hpbw <= 0:
            lobe_hpbw += 2 * np.pi

          candidate_lobes.append((angle, current_intensity, lobe_hpbw))

  # Now identify main lobes based on equivalent intensity AND HPBW
  if len(candidate_lobes) == 0:
    return []
  elif len(candidate_lobes) == 1:
    return [candidate_lobes[0][0]]

  # Find the strongest lobe as reference
  candidate_lobes.sort(key=lambda x: x[1], reverse=True)  # Sort by intensity
  reference_angle, reference_intensity, reference_hpbw = candidate_lobes[0]

  # Define tolerances for equivalence
  intensity_tolerance_db = 0.5  # Within 0.5 dB
  hpbw_tolerance_percent = 0.15  # Within 15% HPBW

  intensity_tolerance = 10.0 ** (-intensity_tolerance_db / 10.0)
  intensity_threshold = intensity_tolerance * reference_intensity
  hpbw_tolerance = hpbw_tolerance_percent * reference_hpbw

  # Find all lobes equivalent to the reference
  main_lobe_angles = []
  for angle, intensity, hpbw in candidate_lobes:
    # Check intensity equivalence
    intensity_equivalent = intensity >= intensity_threshold

    # Check HPBW equivalence
    hpbw_equivalent = abs(hpbw - reference_hpbw) <= hpbw_tolerance

    if intensity_equivalent and hpbw_equivalent:
      main_lobe_angles.append(angle)

  # Debug output
  for angle in main_lobe_angles:
    # Find intensity at this angle
    angle_idx = np.argmin(np.abs(_unwrap_angle(angles - angle)))
    intensity = I_theta[angle_idx]
    intensity_db = 10.0 * np.log10(intensity / global_peak_intensity)
    print(f"  Selected main lobe at {np.degrees(angle):.1f}°: {intensity_db:.1f} dB relative to peak")

  return main_lobe_angles


def calculate_lobe_hpbw(I, cx, cy, lobe_angle, lamb_px, ring_choice_px=None, num_samples=2048, threshold_db=3.0):
  """
  Calculate HPBW boundaries for a specific lobe direction.

  Args:
    I: 2D intensity array
    cx, cy: center coordinates
    lobe_angle: angle of the lobe peak in radians
    lamb_px: wavelength in pixels
    ring_choice_px: optional ring radius in pixels (uses FARFIELD if None)
    num_samples: number of angular samples around the ring
    threshold_db: threshold in dB below peak for beamwidth calculation

  Returns:
    (theta_left, theta_right): HPBW boundary angles for this lobe, or (None, None) if failed
  """
  # Use fixed far-field radius independent of image size for consistent results
  if ring_choice_px is None:
    ring = int(FARFIELD)
  else:
    ring = int(ring_choice_px)

  # Sample intensity around the full ring
  angles = np.linspace(-np.pi, np.pi, num_samples, endpoint=False)
  xs = cx + ring * np.cos(angles)
  ys = cy + ring * np.sin(angles)
  I_theta = _bilinear_sample(I, xs, ys)

  # Find the closest sample to our lobe angle
  angle_diffs = np.abs(_unwrap_angle(angles - lobe_angle))
  lobe_idx = np.argmin(angle_diffs)
  lobe_intensity = I_theta[lobe_idx]

  # Convert dB threshold to linear scale
  threshold_linear = 10.0 ** (-threshold_db / 10.0)
  threshold_level = threshold_linear * lobe_intensity

  # Search to the "left" and "right" of this lobe for HPBW crossings
  def find_cross_from_lobe(start_idx, step):
    i0 = start_idx
    for _ in range(num_samples // 2):  # Don't search more than halfway around
      i1 = (i0 + step) % num_samples
      v0, v1 = I_theta[i0], I_theta[i1]
      if (v0 - threshold_level) * (v1 - threshold_level) <= 0.0:  # crossed or touched
        # unwrap local angles for interpolation
        a0 = angles[i0]
        a1 = angles[i1]
        # make a1 close to a0 (handle wrap)
        if step == -1 and a1 > a0:
          a1 -= 2 * np.pi
        if step == +1 and a1 < a0:
          a1 += 2 * np.pi
        return _interp_crossing(a0, v0, a1, v1, threshold_level)
      i0 = i1
    return None

  theta_left = find_cross_from_lobe(lobe_idx, -1)
  theta_right = find_cross_from_lobe(lobe_idx, +1)

  # Normalize back to (-pi, pi]
  if theta_left is not None:
    theta_left = _unwrap_angle(theta_left)
  if theta_right is not None:
    theta_right = _unwrap_angle(theta_right)

  return theta_left, theta_right


def calculate_directivity_dbi(I, cx, cy, lamb_px, ring_choice_px=None, num_samples=2048):
  """
  Calculate directivity in dBi using peak-to-average intensity ratio on a far-field ring.

  Uses rotational symmetry assumption and compares peak intensity to average intensity
  around the sampling ring to estimate directivity.

  Args:
    I: 2D intensity array
    cx, cy: center coordinates
    lamb_px: wavelength in pixels
    ring_choice_px: optional ring radius in pixels (uses FARFIELD if None)
    num_samples: number of angular samples around the ring

  Returns:
    directivity_dbi: Directivity in dBi, or None if calculation fails
  """
  # Use fixed far-field radius independent of image size for consistent results
  if ring_choice_px is None:
    ring = int(FARFIELD)
  else:
    ring = int(ring_choice_px)

  # Sample intensity around the full ring
  angles = np.linspace(-np.pi, np.pi, num_samples, endpoint=False)
  xs = cx + ring * np.cos(angles)
  ys = cy + ring * np.sin(angles)
  I_theta = _bilinear_sample(I, xs, ys)

  # Find peak intensity
  I_peak = np.max(I_theta)

  # Calculate average intensity around the ring
  I_average = np.mean(I_theta)

  # Calculate directivity: D = I_peak / I_average
  # For a 2D approximation, this gives us the directivity relative to average
  epsilon = 1e-12
  directivity = I_peak / (I_average + epsilon)

  # Convert to dBi
  directivity_dbi = 10.0 * np.log10(directivity + epsilon)

  return directivity_dbi


def extract_polar_data(I, cx, cy, lamb_px, ring_choice_px=None, num_samples=2048):
  """
  Extract intensity vs angle data for polar plotting.

  Args:
    I: 2D intensity array
    cx, cy: center coordinates
    lamb_px: wavelength in pixels
    ring_choice_px: optional ring radius in pixels (uses FARFIELD if None)
    num_samples: number of angular samples around the ring

  Returns:
    angles: array of angles in radians (-π to π)
    intensities: array of intensity values (linear scale, 0-1)
    intensities_db: array of intensity values in dB relative to peak
  """
  # Use fixed far-field radius independent of image size for consistent results
  if ring_choice_px is None:
    ring = int(FARFIELD)
  else:
    ring = int(ring_choice_px)

  # Sample intensity around the full ring
  angles = np.linspace(-np.pi, np.pi, num_samples, endpoint=False)
  xs = cx + ring * np.cos(angles)
  ys = cy + ring * np.sin(angles)
  intensities = _bilinear_sample(I, xs, ys)

  # Convert to dB scale relative to peak
  peak_intensity = np.max(intensities)
  epsilon = 1e-12
  intensities_db = 10.0 * np.log10((intensities + epsilon) / (peak_intensity + epsilon))

  return angles, intensities, intensities_db


def create_polar_plot(angles, intensities_db, antenna_params, title="Antenna Radiation Pattern"):
  """
  Create a polar plot of the antenna radiation pattern.

  Args:
    angles: array of angles in radians
    intensities_db: array of intensity values in dB
    antenna_params: dict with keys: hpbw_rad, theta_peak, theta_left, theta_right,
                   fb_ratio_db, directivity_dbi, is_omnidirectional, main_lobe_angles
    title: plot title

  Returns:
    fig: matplotlib figure object
  """
  # Create polar plot
  fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))

  # Convert angles to degrees for display (matplotlib polar expects radians but displays degrees)
  angles_deg = np.degrees(angles)

  # Plot the radiation pattern
  ax.plot(angles, intensities_db, 'b-', linewidth=2, label='Radiation Pattern')

  # Fill the area under the curve for better visualization
  ax.fill_between(angles, intensities_db, -30, alpha=0.3, color='blue')

  # Set radial limits (dB scale)
  ax.set_ylim(-30, 0)  # 30 dB dynamic range
  # Show grid circles for every 3 dB
  ax.set_yticks(np.arange(-30, 1, 3))
  ax.set_yticklabels([])  # Remove default labels
  # Add custom labels only for every 6 dB: 0, -6, -12, -18, -24, -30
  labeled_ticks = np.arange(-30, 1, 6)
  ax.set_rgrids(labeled_ticks, [f'{y} dB' for y in labeled_ticks], angle=45)

  # Configure angular axis
  ax.set_theta_zero_location('E')  # 0° at East (right)
  ax.set_theta_direction(1)  # Counterclockwise
  ax.grid(True, alpha=0.3)

  return fig, ax


def add_antenna_annotations(ax, angles, intensities_db, antenna_params):
  """
  Add antenna parameter annotations to the polar plot.

  Args:
    ax: matplotlib polar axis
    angles: array of angles in radians
    intensities_db: array of intensity values in dB
    antenna_params: dict with antenna parameters
  """
  if antenna_params['is_omnidirectional']:
    # Add omnidirectional annotation
    ax.text(0, -10, 'Omnidirectional\nPattern', ha='center', va='center',
            bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.7),
            fontsize=12, weight='bold')
    return

  # Mark peak direction(s)
  if antenna_params.get('main_lobe_angles') and len(antenna_params['main_lobe_angles']) > 1:
    # Multiple main lobes (symmetric pattern)
    for i, lobe_angle in enumerate(antenna_params['main_lobe_angles']):
      ax.axvline(lobe_angle, color='yellow', linestyle='--', alpha=0.7, linewidth=1)
  elif antenna_params.get('theta_peak') is not None:
    # Single main lobe
    theta_peak = antenna_params['theta_peak']
    ax.axvline(theta_peak, color='yellow', linestyle='--', alpha=0.7, linewidth=1)



  # Mark HPBW boundaries
  if (antenna_params.get('theta_left') is not None and
      antenna_params.get('theta_right') is not None):
    theta_left = antenna_params['theta_left']
    theta_right = antenna_params['theta_right']

    # Draw HPBW boundary lines
    ax.axvline(theta_left, color='green', linestyle='--', alpha=0.7, linewidth=1)
    ax.axvline(theta_right, color='green', linestyle='--', alpha=0.7, linewidth=1)


def add_parameter_text_box(ax, antenna_params):
  """
  Add a text box with antenna parameters to the polar plot.

  Args:
    ax: matplotlib polar axis
    antenna_params: dict with antenna parameters
  """
  # Prepare parameter text
  param_lines = []

  if antenna_params['is_omnidirectional']:
    param_lines.append("Pattern: Omnidirectional")
  else:
    if antenna_params.get('hpbw_rad') is not None:
      hpbw_deg = np.degrees(antenna_params['hpbw_rad'])
      param_lines.append(f"HPBW: {hpbw_deg:.1f}°")

    if antenna_params.get('theta_peak') is not None:
      peak_deg = np.degrees(antenna_params['theta_peak'])
      param_lines.append(f"Peak: {peak_deg:.1f}°")

    if antenna_params.get('fb_ratio_db') is not None:
      param_lines.append(f"F/B: {antenna_params['fb_ratio_db']:.1f} dB")

  if antenna_params.get('directivity_dbi') is not None:
    param_lines.append(f"Directivity: {antenna_params['directivity_dbi']:.1f} dBi")

  # Add text box
  if param_lines:
    param_text = '\n'.join(param_lines)
    ax.text(0.02, 0.98, param_text, transform=ax.transAxes, fontsize=11,
            verticalalignment='top', bbox=dict(boxstyle='round,pad=0.5',
            facecolor='white', alpha=0.8))


def save_polar_plot(fig, filepath, title="Antenna Radiation Pattern", dpi=150):
  """
  Save the polar plot to file with proper formatting.

  Args:
    fig: matplotlib figure object
    filepath: output file path
    title: plot title (not used - no title displayed)
    dpi: resolution for saved image
  """
  # No title - removed to prevent overlap with polar plot

  # Adjust layout to prevent clipping
  plt.tight_layout()

  # Save with high quality
  fig.savefig(filepath, dpi=dpi, bbox_inches='tight',
              facecolor='white', edgecolor='none')

  # Close figure to free memory
  plt.close(fig)


def load_font_with_weight(size, weight="regular"):
  """Load font with specified weight preference"""
  font_options = {
    "light": [
      "/System/Library/Fonts/Helvetica-Light.ttc",
      "/System/Library/Fonts/Arial.ttf",
      "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf"
    ],
    "regular": [
      "/System/Library/Fonts/Helvetica.ttc",
      "/System/Library/Fonts/Arial.ttf",
      "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
    ],
    "bold": [
      "/System/Library/Fonts/Helvetica-Bold.ttc",
      "/System/Library/Fonts/Arial-Bold.ttf",
      "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf"
    ]
  }

  for font_path in font_options.get(weight, font_options["regular"]):
    try:
      return ImageFont.truetype(font_path, size=size)
    except OSError:
      continue

  return ImageFont.load_default()


def draw_text_enhanced(draw, pos, text, font, fill, outline_color=(0,0,0), letter_spacing=0):
  """Draw text with custom letter spacing and outline"""
  x, y = pos

  if letter_spacing == 0:
    # Standard drawing (faster)
    # Add outline
    for dx in (-1, 1):
      for dy in (-1, 1):
        draw.text((x + dx, y + dy), text, fill=outline_color, font=font)
    draw.text((x, y), text, fill=fill, font=font)
  else:
    # Custom letter spacing
    for char in text:
      # Draw outline
      for dx in (-1, 1):
        for dy in (-1, 1):
          draw.text((x + dx, y + dy), char, fill=outline_color, font=font)
      # Draw character
      draw.text((x, y), char, fill=fill, font=font)
      # Advance position
      char_width = draw.textbbox((0, 0), char, font=font)[2]
      x += char_width + letter_spacing


# Calculate absolute positions
for name, r in RADIATORS.items():
  r["x"] = W//2 + r["offset-x"]
  r["y"] = H//2 + r["offset-y"]

# Validate radiator positions are within image bounds
validation_errors = []
for name, r in RADIATORS.items():
  x, y = r["x"], r["y"]
  if x < 0 or x >= W:
    validation_errors.append(f"Radiator '{name}' x-position ({x:.1f}) is outside image bounds [0, {W-1}]")
  if y < 0 or y >= H:
    validation_errors.append(f"Radiator '{name}' y-position ({y:.1f}) is outside image bounds [0, {H-1}]")

if validation_errors:
  print("ERROR: Radiator position validation failed:")
  for error in validation_errors:
    print(f"  - {error}")
  print("\nSuggestions:")
  print("  1. Reduce the offset values in the RADIATORS configuration")
  print("  2. Increase the image dimensions (W, H)")
  print("  3. Check that offset-x and offset-y values are reasonable for the image size")
  exit(1)

print("Radiator positions validated successfully:")
for name, r in RADIATORS.items():
  print(f"  {name}: ({r['x']:.1f}, {r['y']:.1f}) - amp={r['amp']}, phase={r['phase']}°")

# Enhanced intensity post-processing options
USE_LOG_SCALE = True      # Use logarithmic scaling to reveal low-level signals
LOG_SCALE_FACTOR = 0.1    # Controls log compression (smaller = more compression)
GAMMA = 1.0               # Additional gamma correction (applied after log scaling)
CONTRAST_BOOST = 1.15     # Contrast enhancement factor (1.0 = no change, >1.0 = more contrast)
                          # Values like 1.1-1.3 provide subtle enhancement, 1.5+ more dramatic
HISTOGRAM_STRETCH = False # Apply histogram stretching to use full dynamic range
                          # This maps the actual min/max values to 0/1 for maximum contrast
                          # Disabled to preserve visibility of mid-level signals (like -10dB areas)

# HPBW calculation parameters
HPBW_THRESHOLD_DB = 3.0  # Half-power beamwidth threshold in dB below peak
                         # 3.0 dB = half power (0.5x intensity)
                         # 6.0 dB = quarter power (0.25x intensity)
                         # 10.0 dB = one-tenth power (0.1x intensity)

# Omnidirectional detection parameters
OMNI_DETECTION_THRESHOLD = 0.1  # Maximum variation (as fraction of peak) to consider omnidirectional
                                # 0.1 = 10% variation, 0.05 = 5% variation

# Draw radiator dots
DRAW_DOTS = True
DOT_RADIUS = 8
DOT_FILL_RGB = (255, 255, 255)  # White dots for radiator positions
DOT_OUTLINE = (0, 0, 0)          # Black outline
DOT_OUTLINE_WIDTH = 2

# Output paths
gradient_path = "interference_gradient.png"
polar_path = "interference_polar.png"
# ----------------------


def _draw_disc_rgba(base_img, center_xy, radius, rgb, alpha=255,
                    outline=None, outline_w=0):
  x, y = center_xy
  d = radius * 2
  disc = Image.new("RGBA", (d, d), (0, 0, 0, 0))
  draw = ImageDraw.Draw(disc)
  draw.ellipse([0, 0, d-1, d-1], fill=(rgb[0], rgb[1], rgb[2], alpha))
  if outline and outline_w > 0:
    draw.ellipse([outline_w/2, outline_w/2, d-1-outline_w/2, d-1-outline_w/2],
                 outline=(*outline, 255), width=outline_w)

  if base_img.mode == "L":
    disc_gray = disc.convert("L")
    disc_alpha = disc.getchannel("A")
    base_img.paste(disc_gray, (x - radius, y - radius), disc_alpha)
  else:
    base_img.paste(disc, (x - radius, y - radius), disc)


# --- Field computation (n sources) ---
y, x = np.mgrid[0:H, 0:W]

# Complex field E = sum_j amp_j * exp(i*(k*r_j + phase_j))
E_real = np.zeros((H, W), dtype=np.float64)
E_imag = np.zeros((H, W), dtype=np.float64)

for r in RADIATORS.values():
  dx = x - r["x"]
  dy = y - r["y"]
  dist = np.hypot(dx, dy)
  # phase_total = K * dist + r.get("phase", 0.0)
  phase_total = K * dist + np.deg2rad(r.get("phase", 0.0))
  amp = r.get("amp", 1.0)
  # Add phasor components
  E_real += amp * np.cos(phase_total)
  E_imag += amp * np.sin(phase_total)

# Intensity I ∝ |E|^2
I = E_real**2 + E_imag**2

# Normalize 0..1 by theoretical max (sum of amplitudes)^2
amp_sum = sum(r.get("amp", 1.0) for r in RADIATORS.values())
I /= (amp_sum ** 2 + 1e-12)

# IMPORTANT: Preserve raw intensity data for antenna calculations (HPBW, F/B, Directivity)
# These calculations must use the original, unprocessed intensity field
I_raw = np.clip(I.copy(), 0.0, 1.0)

# Enhanced intensity processing with logarithmic scaling and gamma correction
# This is ONLY for visualization - antenna calculations use I_raw
I_display = I_raw.copy()

if USE_LOG_SCALE:
  # Apply logarithmic scaling to reveal low-level signals
  # Add small epsilon to avoid log(0), then normalize
  epsilon = LOG_SCALE_FACTOR
  I_original_stats = f"Original intensity - Min: {np.min(I_raw):.6f}, Max: {np.max(I_raw):.6f}, Mean: {np.mean(I_raw):.6f}"

  I_log = np.log10(I_display + epsilon) - np.log10(epsilon)
  I_log_max = np.log10(1.0 + epsilon) - np.log10(epsilon)
  I_display = np.clip(I_log / I_log_max, 0.0, 1.0)

  I_log_stats = f"Log-scaled intensity - Min: {np.min(I_display):.6f}, Max: {np.max(I_display):.6f}, Mean: {np.mean(I_display):.6f}"
  print("Enhanced color mapping applied:")
  print(f"  {I_original_stats}")
  print(f"  {I_log_stats}")
  print(f"  Log scale factor: {LOG_SCALE_FACTOR}")

  # Track additional enhancements
  enhancements = []
  if HISTOGRAM_STRETCH:
    enhancements.append("histogram stretching")
  if CONTRAST_BOOST != 1.0:
    enhancements.append(f"contrast boost ({CONTRAST_BOOST}x)")
  if GAMMA != 1.0:
    enhancements.append(f"gamma correction ({GAMMA})")
  if enhancements:
    print(f"  Additional enhancements: {', '.join(enhancements)}")

# Optional additional gamma correction (applied after log scaling)
if GAMMA != 1.0:
  I_display = I_display ** (1.0 / GAMMA)

# Optional histogram stretching (applied before contrast boost)
if HISTOGRAM_STRETCH:
  # Stretch the histogram to use the full 0-1 range for maximum contrast
  I_min, I_max = np.min(I_display), np.max(I_display)
  if I_max > I_min:  # Avoid division by zero
    I_display = (I_display - I_min) / (I_max - I_min)

# Optional contrast enhancement (applied after histogram stretching)
if CONTRAST_BOOST != 1.0:
  # Apply S-curve contrast enhancement around midpoint (0.5)
  # This enhances contrast while preserving the 0-1 range
  I_display = np.clip(((I_display - 0.5) * CONTRAST_BOOST) + 0.5, 0.0, 1.0)

# --- Build images with enhanced color mapping ---
# Create RGB image using vectorized color stop interpolation
# Use I_display (processed) for visualization, I_raw for antenna calculations
H, W = I_display.shape
rgb = np.zeros((H, W, 3), dtype=np.uint8)

# Vectorized color mapping using numpy operations
I_flat = I_display.flatten()
rgb_flat = np.zeros((len(I_flat), 3))

# For each color channel, interpolate across all pixels at once
for channel in range(3):
  # Extract the channel values from all color stops
  positions = np.array([stop[0] for stop in COLOR_STOPS])
  colors = np.array([stop[1][channel] for stop in COLOR_STOPS])

  # Use numpy's interp function for fast vectorized interpolation
  rgb_flat[:, channel] = np.interp(I_flat, positions, colors)

# Reshape back to image dimensions and convert to uint8
rgb = rgb_flat.reshape(H, W, 3).astype(np.uint8)
img_gradient = Image.fromarray(rgb)

# Draw radiator dots
if DRAW_DOTS:
  for r in RADIATORS.values():
    pos = (int(r["x"]), int(r["y"]))
    _draw_disc_rgba(img_gradient, pos, DOT_RADIUS, DOT_FILL_RGB,
                    alpha=255, outline=DOT_OUTLINE, outline_w=DOT_OUTLINE_WIDTH)

# --- ANTENNA CALCULATIONS: Use extended field if needed for consistent far-field analysis ---
# IMPORTANT: Use I_raw (unprocessed) for accurate antenna calculations
cx, cy = W//2, H//2  # center

# Check if we need to compute an extended field for far-field sampling
required_radius = int(FARFIELD)
max_sampling_distance = required_radius + max(cx, cy, W-cx, H-cy)

if max_sampling_distance > min(W, H) // 2:
  # Need extended field calculation for accurate far-field analysis
  print(f"Computing extended field for far-field analysis (radius: {required_radius} px = {required_radius/LAMBDA:.1f}λ)")
  I_extended, offset_x, offset_y = _compute_extended_field(cx, cy, required_radius, RADIATORS, LAMBDA)

  # Adjust center coordinates for the extended field
  cx_ext = cx + offset_x
  cy_ext = cy + offset_y

  # Use extended field for antenna calculations
  I_analysis = I_extended
  cx_analysis, cy_analysis = cx_ext, cy_ext
else:
  # Original field is sufficient
  I_analysis = I_raw
  cx_analysis, cy_analysis = cx, cy

# --- HPBW: estimate from intensity ring and draw on the image ---
theta_left, theta_right, hpbw_rad, theta_peak, is_omnidirectional = estimate_hpbw(
  I_analysis, cx_analysis, cy_analysis, lamb_px=LAMBDA, ring_choice_px=None, num_samples=2048,
  threshold_db=HPBW_THRESHOLD_DB, omni_threshold=OMNI_DETECTION_THRESHOLD,
)

# --- F/B Ratio: calculate front-to-back ratio ---
fb_ratio_db = None
if not is_omnidirectional and theta_peak is not None:
  fb_ratio_db = calculate_fb_ratio(I_analysis, cx_analysis, cy_analysis, theta_peak, lamb_px=LAMBDA)

# --- Main Lobe Detection: find all main lobes for symmetric patterns ---
main_lobe_angles = []
is_symmetric_pattern = False
if not is_omnidirectional and theta_peak is not None:
  main_lobe_angles = find_main_lobes(I_analysis, cx_analysis, cy_analysis, lamb_px=LAMBDA, main_lobe_threshold_db=0.5)
  # Consider it symmetric if we have multiple main lobes or very low F/B ratio
  is_symmetric_pattern = (len(main_lobe_angles) > 1) or (fb_ratio_db is not None and fb_ratio_db < 3.0)

# --- Directivity: calculate directivity in dBi ---
directivity_dbi = calculate_directivity_dbi(I_analysis, cx_analysis, cy_analysis, lamb_px=LAMBDA)

# --- Additional Analysis: Check signal levels at specific angles ---
if not is_omnidirectional and theta_peak is not None:
  # Sample at the far-field ring to get consistent measurements
  ring = int(FARFIELD)

  # Check signal levels at ±135° from peak (the "null" regions)
  angles_to_check = [
    (theta_peak + np.deg2rad(135), "135° from peak"),
    (theta_peak + np.deg2rad(-135), "-135° from peak")
  ]

  print("\nSignal level analysis:")
  for angle, description in angles_to_check:
    # Sample at this angle
    x_sample = cx_analysis + ring * np.cos(angle)
    y_sample = cy_analysis + ring * np.sin(angle)
    intensity_sample = _bilinear_sample(I_analysis, np.array([x_sample]), np.array([y_sample]))[0]

    # Find peak intensity for reference
    angles_full = np.linspace(-np.pi, np.pi, 2048, endpoint=False)
    xs_full = cx_analysis + ring * np.cos(angles_full)
    ys_full = cy_analysis + ring * np.sin(angles_full)
    I_theta_full = _bilinear_sample(I_analysis, xs_full, ys_full)
    peak_intensity = np.max(I_theta_full)

    # Calculate dB difference
    db_down = 10.0 * np.log10(peak_intensity / (intensity_sample + 1e-12))

    print(f"  {description}: {db_down:.1f} dB down from peak")
    print(f"    (intensity: {intensity_sample:.6f} vs peak: {peak_intensity:.6f})")

draw = ImageDraw.Draw(img_gradient)
line_color = (255, 0, 0)  # Red
line_width = 2

# Handle omnidirectional patterns
if is_omnidirectional:
  print("Omnidirectional pattern detected - uniform radiation in all directions")
  print("No directional beam characteristics to display")
  if directivity_dbi is not None:
    print(f"Directivity: {directivity_dbi:.1f} dBi")

  # Add omnidirectional label in upper left corner
  font = load_font_with_weight(TEXT_FONT_SIZE, TEXT_FONT_WEIGHT)

  omni_label = "Omnidirectional"
  # Position label in upper left with margin
  tx, ty = TEXT_MARGIN, TEXT_MARGIN
  draw_text_enhanced(draw, (tx, ty), omni_label, font, (255, 255, 255), letter_spacing=TEXT_LETTER_SPACING)

  # Add directivity label below the omnidirectional label
  if directivity_dbi is not None:
    directivity_label = f"Directivity: {directivity_dbi:.1f} dBi"
    directivity_tx, directivity_ty = TEXT_MARGIN, TEXT_MARGIN + int(TEXT_LINE_HEIGHT)
    draw_text_enhanced(draw, (directivity_tx, directivity_ty), directivity_label, font, (255, 255, 255), letter_spacing=TEXT_LETTER_SPACING)

else:
  # Directional pattern - draw beam characteristics
  # Make lines long enough to reach edges (Pillow will clip)
  L = max(W, H)

  def _ray_to_point(theta):
    return int(cx + L * math.cos(theta)), int(cy + L * math.sin(theta))

  peak_color = (255, 255, 0)  # Yellow

  if is_symmetric_pattern and len(main_lobe_angles) > 1:
    # Symmetric pattern - draw solid peak lines for all main lobes (no back direction lines)
    print(f"Symmetric pattern detected with {len(main_lobe_angles)} main lobes")
    for i, lobe_angle in enumerate(main_lobe_angles):
      draw.line([(cx, cy), _ray_to_point(lobe_angle)], fill=peak_color, width=line_width)
      print(f"  Main lobe {i+1}: {math.degrees(lobe_angle):.1f}°")
  else:
    # Standard directional pattern - draw single peak and back direction
    if theta_peak is not None:
      draw.line([(cx, cy), _ray_to_point(theta_peak)], fill=peak_color, width=line_width)

      # Draw back direction line (semi-transparent yellow) if F/B ratio is available
      # Only for asymmetric patterns - symmetric patterns have no "back" direction
      if fb_ratio_db is not None:
        theta_back = _unwrap_angle(theta_peak + np.pi)
        back_point = _ray_to_point(theta_back)

        # Create a semi-transparent overlay for the back direction line
        overlay = Image.new('RGBA', img_gradient.size, (0, 0, 0, 0))
        overlay_draw = ImageDraw.Draw(overlay)
        back_color_alpha = (*peak_color, 128)  # 50% transparency (128/255)
        overlay_draw.line([(cx, cy), back_point], fill=back_color_alpha, width=line_width)

        # Composite the overlay onto the main image
        img_gradient = Image.alpha_composite(img_gradient.convert('RGBA'), overlay).convert('RGB')

        # Update the draw object after image conversion
        draw = ImageDraw.Draw(img_gradient)

  # Check if HPBW calculation was successful
  if theta_left is not None and theta_right is not None:
    hpbw_deg = math.degrees(hpbw_rad)

    if is_symmetric_pattern and len(main_lobe_angles) > 1:
      # For symmetric patterns, calculate and draw HPBW for each main lobe
      for lobe_angle in main_lobe_angles:
        lobe_left, lobe_right = calculate_lobe_hpbw(
          I_analysis, cx_analysis, cy_analysis, lobe_angle, lamb_px=LAMBDA,
          threshold_db=HPBW_THRESHOLD_DB
        )

        if lobe_left is not None and lobe_right is not None:
          draw.line([(cx, cy), _ray_to_point(lobe_left)], fill=line_color, width=line_width)
          draw.line([(cx, cy), _ray_to_point(lobe_right)], fill=line_color, width=line_width)
    else:
      # Standard single-lobe pattern
      draw.line([ (cx, cy), _ray_to_point(theta_left) ], fill=line_color, width=line_width)
      draw.line([ (cx, cy), _ray_to_point(theta_right) ], fill=line_color, width=line_width)

    # Label: place in upper left corner with margin
    if HPBW_THRESHOLD_DB == 3.0:
      label = f"HPBW: {hpbw_deg:.1f}º"  # Standard -3dB notation
    else:
      label = f"BW(-{HPBW_THRESHOLD_DB:.1f}dB) = {hpbw_deg:.1f}º"  # Show custom threshold

    font = load_font_with_weight(TEXT_FONT_SIZE, TEXT_FONT_WEIGHT)

    # Position the text in upper left corner with margin
    tx = TEXT_MARGIN
    ty = TEXT_MARGIN

    draw_text_enhanced(draw, (tx, ty), label, font, line_color, letter_spacing=TEXT_LETTER_SPACING)

    print(f"HPBW successfully calculated: {hpbw_deg:.1f}°")
    print(f"Peak direction: {math.degrees(theta_peak):.1f}°")
    if fb_ratio_db is not None:
      print(f"F/B Ratio: {fb_ratio_db:.1f} dB")
    if directivity_dbi is not None:
      print(f"Directivity: {directivity_dbi:.1f} dBi")

      # Add F/B ratio label to image (positioned below HPBW label in upper left)
      fb_label = f"F/B Ratio: {fb_ratio_db:.1f} dB"
      fb_tx = TEXT_MARGIN
      fb_ty = TEXT_MARGIN + int(TEXT_LINE_HEIGHT)  # Position below HPBW label

      draw_text_enhanced(draw, (fb_tx, fb_ty), fb_label, font, line_color, letter_spacing=TEXT_LETTER_SPACING)

    # Add directivity label to image (positioned below F/B label or HPBW if no F/B in upper left)
    if directivity_dbi is not None:
      directivity_label = f"Directivity: {directivity_dbi:.1f} dBi"
      directivity_tx = TEXT_MARGIN
      if fb_ratio_db is not None:
        directivity_ty = TEXT_MARGIN + int(TEXT_LINE_HEIGHT * 2)  # Position below F/B label
      else:
        directivity_ty = TEXT_MARGIN + int(TEXT_LINE_HEIGHT)  # Position below HPBW label

      draw_text_enhanced(draw, (directivity_tx, directivity_ty), directivity_label, font, line_color, letter_spacing=TEXT_LETTER_SPACING)
  else:
    # HPBW calculation failed - provide fallback behavior
    print("Warning: Could not determine HPBW - no clear -3dB crossings found")
    print("This may occur with very broad beams, very narrow beams, or unusual patterns")
    if theta_peak is not None:
      print(f"Peak direction: {math.degrees(theta_peak):.1f}°")
      if fb_ratio_db is not None:
        print(f"F/B Ratio: {fb_ratio_db:.1f} dB")
      if directivity_dbi is not None:
        print(f"Directivity: {directivity_dbi:.1f} dBi")

      # Add warning label in upper left corner
      warning_font = load_font_with_weight(TEXT_FONT_SIZE, TEXT_FONT_WEIGHT)

      warning_label = "HPBW: N/A"
      tx = TEXT_MARGIN
      ty = TEXT_MARGIN

      draw_text_enhanced(draw, (tx, ty), warning_label, warning_font, peak_color, letter_spacing=TEXT_LETTER_SPACING)

# Save interference map
img_gradient.save(gradient_path)

# --- GENERATE POLAR PLOT ---
print("Generating polar plot...")

# Extract polar data using the same analysis field and parameters as antenna calculations
angles, intensities, intensities_db = extract_polar_data(
  I_analysis, cx_analysis, cy_analysis, lamb_px=LAMBDA
)

# Prepare antenna parameters for polar plot
antenna_params = {
  'hpbw_rad': hpbw_rad,
  'theta_peak': theta_peak,
  'theta_left': theta_left,
  'theta_right': theta_right,
  'fb_ratio_db': fb_ratio_db,
  'directivity_dbi': directivity_dbi,
  'is_omnidirectional': is_omnidirectional,
  'main_lobe_angles': main_lobe_angles if not is_omnidirectional else []
}

# Create polar plot
fig, ax = create_polar_plot(angles, intensities_db, antenna_params,
                           title="Antenna Radiation Pattern")

# Add annotations
add_antenna_annotations(ax, angles, intensities_db, antenna_params)
add_parameter_text_box(ax, antenna_params)

# Save polar plot
save_polar_plot(fig, polar_path, title="Antenna Radiation Pattern")

print(f"Interference map saved: {gradient_path}")
print(f"Polar plot saved: {polar_path}")

import random
import argpar<PERSON>

def format_time(total_minutes):
    """Convert total minutes to days:hours:minutes format"""
    days = int(total_minutes // (24 * 60))
    hours = int((total_minutes % (24 * 60)) // 60)
    minutes = int(total_minutes % 60)
    return f"{days}:{hours:02d}:{minutes:02d}"

def simulate_excavation(tunnel_length=100, tunnel_width=5, tunnel_height=5,
                        skill_base=12, initial_ml=36, trials=100000):
    total_cubic_feet = tunnel_length * tunnel_width * tunnel_height
    total_rolls_list = []
    total_minutes_list = []
    total_mls_list = []

    for _ in range(trials):
        ml = initial_ml
        excavated = 0
        rolls = 0
        minutes_elapsed = 0

        while excavated < total_cubic_feet:
            rolls += 1
            roll = random.randint(1, 100)
            skill_index = ml // 10
            cubic_per_minute = skill_index ** 2
            casting_time = 15 - skill_index
            minutes_elapsed += casting_time

            if roll <= ml:
                if roll % 10 == 0 or roll % 10 == 5:
                    # Critical success: 5 * SI minutes
                    excavated += 5 * (skill_index ** 3)
                    duration = 5 * skill_index
                else:
                    # Marginal success: 3 * SI minutes
                    excavated += 3 * (skill_index ** 3)
                    duration = 3 * skill_index
                minutes_elapsed += max(duration, 10)
            else:
                minutes_elapsed += 10

            # Skill Development Roll
            sdr_roll = random.randint(1, 100)
            if sdr_roll + skill_base > ml:
                ml += 1

        total_rolls_list.append(rolls)
        total_minutes_list.append(minutes_elapsed)
        total_mls_list.append(ml)

    avg_rolls = sum(total_rolls_list) / len(total_rolls_list)
    avg_minutes = sum(total_minutes_list) / len(total_minutes_list)
    avg_ml = sum(total_mls_list) / len(total_mls_list)
    return avg_rolls, avg_minutes, avg_ml

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Simulate magical excavation of a tunnel.")
    parser.add_argument("--length", type=int, default=100, help="Tunnel length in feet (default: 100)")
    parser.add_argument("--width", type=int, default=5, help="Tunnel width in feet (default: 5)")
    parser.add_argument("--height", type=int, default=5, help="Tunnel height in feet (default: 5)")
    parser.add_argument("--ml", type=int, default=36, help="Initial Mastery Level (default: 36)")
    parser.add_argument("--sb", type=int, default=12, help="Skill Base for SDRs (default: 12)")
    parser.add_argument("--trials", type=int, default=10000, help="Number of simulation trials (default: 20000)")

    args = parser.parse_args()

    (avg_rolls, avg_minutes, avg_ml) = simulate_excavation(
        tunnel_length=args.length,
        tunnel_width=args.width,
        tunnel_height=args.height,
        skill_base=args.sb,
        initial_ml=args.ml,
        trials=args.trials
    )

    print(f"Expected rolls to excavate a {args.length}x{args.width}x{args.height} tunnel with SB {args.sb} and starting ML {args.ml}: {avg_rolls:.2f}")
    print(f"Expected total time to excavate: {format_time(avg_minutes)} (days:hours:minutes)")
    print(f"Expected final Mastery Level: {avg_ml:.2f}")


import argparse

def expected_sdrs_to_reach(target_ml, starting_ml, skill_base):
    if target_ml <= starting_ml:
        return 0

    total_expected_rolls = 0.0

    for ml in range(starting_ml, target_ml):
        success_chance = max(0, (100 + skill_base - ml - 1)) / 100
        if success_chance == 0:
            return float("inf")  # Impossible to advance
        total_expected_rolls += 1 / success_chance

    return total_expected_rolls

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Calculate expected SDRs to reach a target Mastery Level.")
    parser.add_argument("--sb", type=int, default=12, help="Skill Base (default: 12)")
    parser.add_argument("--ml", type=int, default=36, help="Starting Mastery Level (default: 36)")
    parser.add_argument("--target", type=int, required=True, help="Desired Mastery Level (required)")

    args = parser.parse_args()

    expected = expected_sdrs_to_reach(args.target, args.ml, args.sb)
    print(f"Expected SDRs to go from ML {args.ml} to {args.target} (SB={args.sb}): {expected:.2f}")

